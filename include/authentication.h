#ifndef AUTHENTICATION_H
#define AUTHENTICATION_H

#include <Arduino.h>
#include <Adafruit_PN532.h>

// 认证结果结构
struct AuthResult {
  bool success;
  String uid;
  String message;
};

// 认证管理类
class Authentication {
private:
  Adafruit_PN532* nfc;
  uint8_t defaultKey[6] = { 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF };
  int consecutiveErrors;
  unsigned long lastSuccessfulOperation;

  static const uint8_t SECTOR_TRAILER_BLOCK = 7;
  static const uint8_t AUTH_BLOCK = 4;
  static const uint8_t KEY_SIZE = 6;
  static const uint8_t TRAILER_SIZE = 16;
  static const int MAX_CONSECUTIVE_ERRORS = 3;

public:
  Authentication(Adafruit_PN532* nfcInstance);
  
  // 初始化
  bool initialize();
  
  // IRQ-based 检测
  bool startPassiveDetection();
  AuthResult readDetectedCard();
  
  // 卡片认证
  AuthResult authenticateCard();
  
  // 卡片注册
  AuthResult registerCard();
  
  // 错误恢复
  bool checkNFCHealth();
  void resetPN532();
  void recordError();
  void recordSuccess();

  // 工具函数
  String uidToString(uint8_t* uid, uint8_t len);
  void generateRandomKey(uint8_t* key);
  String keyToHexString(uint8_t* key);
  void hexStringToKey(const String& hexString, uint8_t* key);
  
private:
  // 内部认证函数
  bool readCardUID(uint8_t* uid, uint8_t* uidLen);
  bool authenticateBlock(uint8_t* uid, uint8_t uidLen, uint8_t blockNumber, uint8_t* key);
  bool writeSectorTrailer(uint8_t* newKey);
};

#endif // AUTHENTICATION_H
