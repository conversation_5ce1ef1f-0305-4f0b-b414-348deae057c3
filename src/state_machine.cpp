#include "state_machine.h"
#include "authentication.h"
#include "actuator.h"
#include "card_manager.h"

// 全局实例声明
extern Authentication auth;
extern Actuator actuator;
extern CardManager cardManager;

// PN532 IRQ引脚定义
#define PN532_IRQ 34

StateMachine::StateMachine() 
  : currentState(WAITING_FOR_CARD)
  , cooldownStartTime(0)
  , detectionStartTime(0)
  , lastCardUID("")
  , irqPrev(HIGH)
  , irqCurr(HIGH)
  , registrationMode(false) {
}

void StateMachine::setState(SystemState newState) {
  if (currentState != newState) {
    Serial.print("State change: ");
    Serial.print(currentState);
    Serial.print(" -> ");
    Serial.println(newState);
    currentState = newState;
  }
}

SystemState StateMachine::getState() const {
  return currentState;
}

void StateMachine::update() {
  updateIRQ();

  switch (currentState) {
    case WAITING_FOR_CARD:
      // 在等待状态下，如果检测到IRQ触发，处理卡片
      if (isIRQTriggered()) {
        Serial.println("Card detected in WAITING state");
        AuthResult result = auth.readDetectedCard();
        if (result.success) {
          onCardDetected(result.uid);
        } else {
          // 读取失败，重新开始检测
          Serial.println("Failed to read card, restarting detection");
          delay(100);
          startDetection();
        }
      }
      break;

    case COOLDOWN:
      // 冷却期状态处理
      if (isCooldownExpired()) {
        Serial.println("Cooldown expired, returning to WAITING state");
        setState(WAITING_FOR_CARD);
        startDetection();
      } else if (isIRQTriggered()) {
        Serial.println("Card detected during cooldown");
        AuthResult result = auth.readDetectedCard();
        if (result.success) {
          if (shouldBypassCooldown(result.uid)) {
            Serial.println("Different card detected, bypassing cooldown");
            setState(WAITING_FOR_CARD);
            onCardDetected(result.uid);
          } else {
            Serial.println("Same card detected, restarting cooldown");
            delay(SAME_CARD_DELAY);
            startDetection();
            cooldownStartTime = millis();
          }
        } else {
          // 读取失败，重新开始检测
          Serial.println("Failed to read card during cooldown, restarting detection");
          delay(SAME_CARD_DELAY);
          startDetection();
        }
      }
      break;

    case REGISTRATION:
      // 注册模式处理
      if (isIRQTriggered()) {
        Serial.println("Card detected in REGISTRATION mode");
        AuthResult result = auth.registerCard();
        if (result.success) {
          actuator.indicateRegistrationSuccess();
          Serial.println("Card registered successfully: " + result.uid);
        } else {
          actuator.indicateRegistrationFailure();
          Serial.println("Registration failed: " + result.message);
        }
        exitRegistrationMode();
      }
      break;
  }
}

void StateMachine::onCardDetected(const String& uid) {
  if (registrationMode) {
    // 注册模式下的处理在update()中完成
    return;
  }
  
  // 执行认证
  AuthResult authResult = auth.authenticateCard();
  onAuthenticationResult(authResult.success, uid);
}

void StateMachine::onAuthenticationResult(bool success, const String& uid) {
  if (success) {
    Serial.println("Authentication successful for UID: " + uid);
    actuator.indicateAuthSuccess();
  } else {
    Serial.println("Authentication failed for UID: " + uid);
    actuator.indicateAuthFailure();
  }
  
  // 进入冷却期
  startCooldown(uid);
}

void StateMachine::onRegistrationComplete() {
  setState(WAITING_FOR_CARD);
  startDetection();
}

void StateMachine::enterRegistrationMode() {
  registrationMode = true;
  setState(REGISTRATION);
  actuator.indicateRegistrationMode();
  startDetection();
  Serial.println("Entered registration mode - tap blank card to register");
}

void StateMachine::exitRegistrationMode() {
  registrationMode = false;
  setState(WAITING_FOR_CARD);
  startDetection();
  Serial.println("Exited registration mode");
}

bool StateMachine::isInRegistrationMode() const {
  return registrationMode;
}

void StateMachine::updateIRQ() {
  irqCurr = digitalRead(PN532_IRQ);
}

bool StateMachine::isIRQTriggered() const {
  bool triggered = (irqCurr == LOW && irqPrev == HIGH);
  if (triggered) {
    // 更新irqPrev以避免重复触发
    const_cast<StateMachine*>(this)->irqPrev = irqCurr;
  }
  return triggered;
}

void StateMachine::startCooldown(const String& uid) {
  lastCardUID = uid;
  cooldownStartTime = millis();
  setState(COOLDOWN);
  startDetection();  // 立即发起下次检测
}

bool StateMachine::isCooldownExpired() const {
  return (millis() - cooldownStartTime) > COOLDOWN_TIMEOUT;
}

bool StateMachine::shouldBypassCooldown(const String& uid) const {
  return uid != lastCardUID;
}

void StateMachine::startDetection() {
  detectionStartTime = millis();
  irqPrev = irqCurr = HIGH;  // 重置IRQ状态
  
  if (!auth.startPassiveDetection()) {
    Serial.println("Failed to start passive detection");
  }
}

bool StateMachine::isDetectionActive() const {
  return detectionStartTime > 0;
}

unsigned long StateMachine::getDetectionDuration() const {
  if (detectionStartTime == 0) return 0;
  return millis() - detectionStartTime;
}


