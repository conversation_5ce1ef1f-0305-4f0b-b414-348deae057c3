// Door Access System using ESP32, PN532, and MIFARE Classic
// Features:
// 1. Automatic card authentication in main loop using state machine
// 2. Register blank card: generate random key, write to sector trailer (block 7), record mapping in SPIFFS JSON
// 3. List and delete cards: manage registered cards via Serial commands
// 4. Smart cooldown mechanism to prevent duplicate reads from same card

#include <Arduino.h>
#include <Wire.h>
#include <SPI.h>
#include <SPIFFS.h>
#include <ArduinoJson.h>
#include <Adafruit_PN532.h>

// =============================================================================
// 配置
// =============================================================================
// PN532 I2C pins
#define PN532_IRQ   34
#define PN532_RESET 5

// LED pin
#define LED_PIN 2

// MIFARE Classic settings
#define SECTOR_TRAILER_BLOCK 7
#define AUTH_BLOCK 4
#define KEY_SIZE 6
#define TRAILER_SIZE 16

// 文件系统
const char* CARD_FILE = "/cards.json";

// 时间常量
const unsigned long COOLDOWN_DURATION = 1000;      // 1秒冷却期
const unsigned long REGISTRATION_TIMEOUT = 10000;   // 10秒注册超时
const unsigned long SAME_CARD_DELAY = 100;         // 相同卡片延迟100ms

// =============================================================================
// 状态机定义
// =============================================================================
enum SystemState {
  WAITING_FOR_CARD,
  AUTHENTICATING,
  COOLDOWN,
  REGISTRATION_MODE,
  REGISTERING
};

struct CardInfo {
  String lastUID;
  unsigned long lastDetectionTime;
  unsigned long cooldownStartTime;
  unsigned long registrationStartTime;
  unsigned long lastCooldownResetTime; // ✅ 新增
  int cooldownResetCount; // ✅ 新增：防止过度重置
  bool isValid;
};

// =============================================================================
// 全局变量
// =============================================================================
Adafruit_PN532 nfc(PN532_IRQ, PN532_RESET);
JsonDocument cardDatabase;
uint8_t defaultKey[KEY_SIZE] = { 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF };

SystemState currentState = WAITING_FOR_CARD;
CardInfo cardInfo = {"", 0, 0, 0, false};
int irqCurrent = HIGH;
int irqPrevious = HIGH;
unsigned long lastSuccessfulOperation = 0;
int consecutiveErrors = 0;

// =============================================================================
// 函数声明
// =============================================================================
bool initializeNFC();
bool checkNFCHealth();
void handleCardDetected();
void performDeepPN532Reset();
String uidToString(uint8_t* uid, uint8_t len);

// =============================================================================
// 状态机函数
// =============================================================================
void setState(SystemState newState) {
  if (currentState != newState) {
    Serial.print("State change: ");
    Serial.print(currentState);
    Serial.print(" -> ");
    Serial.println(newState);
    currentState = newState;
  } else {
    Serial.print("State remains: ");
    Serial.println(currentState);
  }
}

void startNFCDetection() {
  if (!checkNFCHealth()) {
    Serial.println("NFC health check failed, skipping detection start");
    return;
  }

  // ✅ 添加状态清理
  irqPrevious = irqCurrent = HIGH;

  // ✅ 添加预检查，避免在异常状态下启动检测
  uint32_t version = nfc.getFirmwareVersion();
  if (!version) {
    Serial.println("PN532 not responding, attempting recovery...");
    consecutiveErrors++;
    if (!checkNFCHealth()) {
      return;
    }
  }

  Serial.println("Starting passive read for an ISO14443A Card ...");

  bool detectionStarted = false;
  for (int retry = 0; retry < 3; retry++) {
    // ✅ 添加延迟，确保PN532准备就绪
    if (retry > 0) {
      delay(200);
    }

    if (nfc.startPassiveTargetIDDetection(PN532_MIFARE_ISO14443A)) {
      Serial.println("Card already present.");

      // 只在非冷却期处理立即检测到的卡片
      if (currentState != COOLDOWN) {
        handleCardDetected();
      } else {
        Serial.println("In cooldown - card still present, ignoring");
      }
      detectionStarted = true;
      break;

    } else {
      if (retry == 0) {
        Serial.println("No card found. Waiting...");
        detectionStarted = true;
        break;
      } else {
        Serial.print("Detection failed, retry ");
        Serial.println(retry + 1);
        consecutiveErrors++;
      }
    }
  }

  if (detectionStarted) {
    consecutiveErrors = 0;
    lastSuccessfulOperation = millis();
  } else {
    Serial.println("Failed to start detection after retries");
    consecutiveErrors++;
  }
}


void handleCardDetected() {
  uint8_t success = false;
  uint8_t uid[] = { 0, 0, 0, 0, 0, 0, 0 };  // Buffer to store the returned UID
  uint8_t uidLength;                        // Length of the UID

  // Read the NFC tag's info
  success = nfc.readDetectedPassiveTargetID(uid, &uidLength);
  Serial.println(success ? "Read successful" : "Read failed (not a card?)");

  if (success) {
    String uidStr = uidToString(uid, uidLength);
    Serial.print("Got NFC IRQ - Card detected: ");
    Serial.println(uidStr);

    // Update card info and trigger state machine
    cardInfo.lastUID = uidStr;
    cardInfo.lastDetectionTime = millis();
    consecutiveErrors = 0;

    // Handle based on current state - but NOT if in cooldown
    if (currentState == WAITING_FOR_CARD) {
      setState(AUTHENTICATING);
    } else if (currentState == REGISTRATION_MODE) {
      setState(REGISTERING);
    } else if (currentState == COOLDOWN) {
      // This should be handled by handleCooldown() instead
      Serial.println("Card detected during cooldown - will be handled by cooldown logic");
    }
  }
}

// =============================================================================
// 工具函数
// =============================================================================
String uidToString(uint8_t* uid, uint8_t len) {
  String result;
  for (uint8_t i = 0; i < len; i++) {
    if (uid[i] < 0x10) result += "0";
    result += String(uid[i], HEX);
  }
  result.toUpperCase();
  return result;
}

void generateRandomKey(uint8_t* key) {
  for (int i = 0; i < KEY_SIZE; i++) {
    key[i] = random(0, 256);
  }
}

String keyToHexString(uint8_t* key) {
  char keyHex[13] = {0};
  for (int i = 0; i < KEY_SIZE; i++) {
    sprintf(&keyHex[i * 2], "%02X", key[i]);
  }
  return String(keyHex);
}

void hexStringToKey(const String& hexString, uint8_t* key) {
  for (int i = 0; i < KEY_SIZE; i++) {
    key[i] = strtoul(hexString.substring(i * 2, i * 2 + 2).c_str(), NULL, 16);
  }
}

String readCurrentCardUID() {
  uint8_t uid[7], uidLen;
  
  // Try multiple times to read the detected card
  for (int retry = 0; retry < 3; retry++) {
    if (nfc.readDetectedPassiveTargetID(uid, &uidLen)) {
      String uidStr = uidToString(uid, uidLen);
      Serial.print("Read UID: ");
      Serial.println(uidStr);
      consecutiveErrors = 0;
      return uidStr;
    }
    
    if (retry < 2) {
      Serial.print("Failed to read UID, retry ");
      Serial.println(retry + 1);
      delay(50);
    }
  }
  
  Serial.println("Failed to read detected card UID after retries");
  consecutiveErrors++;
  return "";
}

// =============================================================================
// LED 控制
// =============================================================================
void blinkLED(int times = 3, int delayMs = 100) {
  for (int i = 0; i < times; i++) {
    digitalWrite(LED_PIN, HIGH);
    delay(delayMs);
    digitalWrite(LED_PIN, LOW);
    delay(delayMs);
  }
}

// =============================================================================
// 文件系统操作
// =============================================================================
void saveCards() {
  File file = SPIFFS.open(CARD_FILE, FILE_WRITE);
  if (!file) {
    Serial.println("Failed to open card file for writing");
    return;
  }
  serializeJsonPretty(cardDatabase, file);
  file.close();
}

void loadCards() {
  File file = SPIFFS.open(CARD_FILE, FILE_READ);
  if (!file) {
    // Create empty array if file doesn't exist
    cardDatabase.to<JsonArray>();
    saveCards();
  } else {
    DeserializationError err = deserializeJson(cardDatabase, file);
    if (err) {
      // Reset if file is corrupt
      Serial.println("Card database corrupt, resetting...");
      cardDatabase.to<JsonArray>();
      saveCards();
    }
    file.close();
  }
}

// =============================================================================
// NFC 操作
// =============================================================================
bool authenticateBlock(uint8_t* uid, uint8_t uidLen, uint8_t blockNumber, uint8_t* key) {
  return nfc.mifareclassic_AuthenticateBlock(uid, uidLen, blockNumber, 0, key);
}

bool writeSectorTrailer(uint8_t* newKey) {
  uint8_t trailer[TRAILER_SIZE];
  
  // Set new KeyA
  memcpy(trailer, newKey, KEY_SIZE);
  
  // Set default access bits
  memcpy(trailer + 6, "\xFF\x07\x80\x69", 4);
  
  // Keep default KeyB
  memcpy(trailer + 10, defaultKey, KEY_SIZE);
  
  return nfc.mifareclassic_WriteDataBlock(SECTOR_TRAILER_BLOCK, trailer);
}

// =============================================================================
// 卡片管理
// =============================================================================
bool findCardByUID(const String& uid, String& keyHex) {
  JsonArray cards = cardDatabase.as<JsonArray>();
  for (JsonObject card : cards) {
    if (card["uid"] == uid) {
      keyHex = card["key"].as<String>();
      return true;
    }
  }
  return false;
}

bool isCardRegistered(const String& uid) {
  JsonArray cards = cardDatabase.as<JsonArray>();
  for (JsonObject card : cards) {
    if (card["uid"] == uid) {
      return true;
    }
  }
  return false;
}

bool addCardToDatabase(const String& uid, const String& keyHex) {
  JsonArray cards = cardDatabase.as<JsonArray>();
  JsonObject newCard = cards.add<JsonObject>();
  newCard["uid"] = uid;
  newCard["key"] = keyHex;
  saveCards();
  return true;
}

bool removeCardFromDatabase(const String& uid) {
  JsonArray cards = cardDatabase.as<JsonArray>();
  for (size_t i = 0; i < cards.size(); i++) {
    if (cards[i]["uid"] == uid) {
      cards.remove(i);
      saveCards();
      return true;
    }
  }
  return false;
}

// =============================================================================
// 鉴权函数
// =============================================================================
bool performAuthentication(const String& uid) {
  Serial.print("Authenticating card: ");
  Serial.println(uid);

  // Find card in database
  String keyHex;
  if (!findCardByUID(uid, keyHex)) {
    Serial.println("Card not registered");
    return false;
  }

  // Get stored key and authenticate
  uint8_t key[KEY_SIZE];
  hexStringToKey(keyHex, key);

  // Try to read card UID with retries
  uint8_t uidBytes[7], uidLen;
  bool uidReadSuccess = false;

  for (int retry = 0; retry < 3; retry++) {
    if (nfc.readDetectedPassiveTargetID(uidBytes, &uidLen)) {
      uidReadSuccess = true;
      break;
    }

    Serial.print("Failed to read UID for auth, retry ");
    Serial.println(retry + 1);

    // Try to restart detection
    if (retry < 2) {
      nfc.startPassiveTargetIDDetection(PN532_MIFARE_ISO14443A);
      delay(100);
    }
  }

  if (!uidReadSuccess) {
    Serial.println("Failed to read card UID for authentication");
    return false;
  }

  if (authenticateBlock(uidBytes, uidLen, AUTH_BLOCK, key)) {
    Serial.println("Authentication successful");
    blinkLED(3, 100);  // Success blink
    return true;
  } else {
    Serial.println("Authentication failed");
    blinkLED(1, 500);  // Failure blink
    return false;
  }
}

void performCardRegistration(const String& uid) {
  Serial.print("Registering card with UID: ");
  Serial.println(uid);

  // Check if card is already registered
  if (isCardRegistered(uid)) {
    Serial.println("Card already registered");
    return;
  }

  // Get UID bytes for authentication
  uint8_t uidBytes[7], uidLen;
  if (!nfc.readDetectedPassiveTargetID(uidBytes, &uidLen)) {
    Serial.println("Failed to read card UID");
    return;
  }

  // Authenticate with default key
  if (!authenticateBlock(uidBytes, uidLen, SECTOR_TRAILER_BLOCK, defaultKey)) {
    Serial.println("Authentication with default key failed");
    return;
  }

  // Generate and write new key
  uint8_t newKey[KEY_SIZE];
  generateRandomKey(newKey);

  if (!writeSectorTrailer(newKey)) {
    Serial.println("Failed to write sector trailer");
    return;
  }

  // Add to database
  String keyHex = keyToHexString(newKey);
  if (addCardToDatabase(uid, keyHex)) {
    Serial.println("Card registered successfully");
    blinkLED(5, 100);  // Registration success blink
  } else {
    Serial.println("Failed to save card to database");
  }
}

// =============================================================================
// 状态机处理函数
// =============================================================================
void handleWaitingForCard() {
  // Use IRQ-based detection exactly like the working example
  irqCurrent = digitalRead(PN532_IRQ);

  // When the IRQ is pulled low - the reader has got something for us
  if (irqCurrent == LOW && irqPrevious == HIGH) {
    Serial.println("Got NFC IRQ");
    handleCardDetected();
  }

  irqPrevious = irqCurrent;
}

void handleAuthenticating() {
  if (cardInfo.lastUID.length() > 0) {
    performAuthentication(cardInfo.lastUID);

    // Enter cooldown period
    cardInfo.cooldownStartTime = millis();
    setState(COOLDOWN);

    // 立即发起 startPassiveTargetIDDetection（冷却期的关键行为）
    Serial.println("Entering cooldown, starting passive detection...");
    startNFCDetection();
  }
}

void handleCooldown() {
    irqCurrent = digitalRead(PN532_IRQ);

    if (irqCurrent == LOW && irqPrevious == HIGH) {
        Serial.println("Got NFC IRQ during cooldown");

        uint8_t uid[] = { 0, 0, 0, 0, 0, 0, 0 };
        uint8_t uidLength;

        if (nfc.readDetectedPassiveTargetID(uid, &uidLength)) {
            String currentUID = uidToString(uid, uidLength);
            Serial.print("Cooldown IRQ - Card UID: ");
            Serial.println(currentUID);

            if (currentUID == cardInfo.lastUID) {
                // ✅ 关键修正：相同卡片时重置冷却时间
                Serial.println("Same card during cooldown - resetting cooldown timer");
                cardInfo.cooldownStartTime = millis(); // 重置冷却开始时间

                delay(SAME_CARD_DELAY);

                // ✅ 重新发起检测
                if (checkNFCHealth()) {
                    startNFCDetection();
                }
            } else {
                // 不同卡片立即响应
                Serial.println("Different card during cooldown - bypassing cooldown");
                cardInfo.lastUID = currentUID;
                cardInfo.lastDetectionTime = millis();
                setState(AUTHENTICATING);
                return;
            }
        } else {
            Serial.println("Failed to read card during cooldown IRQ");
            delay(100);
            if (checkNFCHealth()) {
                startNFCDetection();
            }
        }
    }

    // 检查冷却期超时
    if (millis() - cardInfo.cooldownStartTime > COOLDOWN_DURATION) {
        Serial.println("Cooldown timeout - checking card presence before restart");

        // ✅ 关键修正：冷却期结束前先检查卡片是否还在
        uint8_t uid[7], uidLen;
        if (nfc.readPassiveTargetID(PN532_MIFARE_ISO14443A, uid, &uidLen, 100)) {
            String currentUID = uidToString(uid, uidLen);
            if (currentUID == cardInfo.lastUID) {
                // 相同卡片还在，延长冷却期
                Serial.println("Same card still present - extending cooldown");
                cardInfo.cooldownStartTime = millis();
                return;
            }
        }

        // 卡片已移除或检测失败，正常结束冷却期
        Serial.println("Cooldown completed - returning to waiting state");
        setState(WAITING_FOR_CARD);

        // ✅ 添加延迟，确保PN532状态稳定
        delay(200);
        if (checkNFCHealth()) {
            startNFCDetection();
        }
    }

    irqPrevious = irqCurrent;
}

void handleRegistrationMode() {
  irqCurrent = digitalRead(PN532_IRQ);

  if (irqCurrent == LOW && irqPrevious == HIGH) {
    Serial.println("Got NFC IRQ - Card detected for registration!");
    handleCardDetected(); // This will set state to REGISTERING
  } else {
    // Check registration timeout
    if (millis() - cardInfo.registrationStartTime > REGISTRATION_TIMEOUT) {
      Serial.println("Registration timeout, returning to normal mode");
      setState(WAITING_FOR_CARD);
      startNFCDetection();
    }
  }

  irqPrevious = irqCurrent;
}


void handleRegistering() {
  if (cardInfo.lastUID.length() > 0) {
    performCardRegistration(cardInfo.lastUID);
    setState(WAITING_FOR_CARD);
    startNFCDetection();
  }
}

// =============================================================================
// 命令处理
// =============================================================================
void handleRegisterCommand() {
  Serial.println("-- Registration mode activated --");
  Serial.println("-- Tap blank card within 10 seconds --");

  cardInfo.registrationStartTime = millis();
  setState(REGISTRATION_MODE);
  startNFCDetection();
}

void handleListCommand() {
  Serial.println("-- Registered Cards --");
  JsonArray cards = cardDatabase.as<JsonArray>();

  if (cards.size() == 0) {
    Serial.println("No cards registered");
    return;
  }

  for (JsonObject card : cards) {
    Serial.print(card["uid"].as<const char*>());
    Serial.print(" : ");
    Serial.println(card["key"].as<const char*>());
  }
}

void handleDeleteCommand(const String& uid) {
  if (uid.length() == 0) {
    Serial.println("Usage: del <UID>");
    return;
  }

  if (removeCardFromDatabase(uid)) {
    Serial.println("Deleted " + uid);
  } else {
    Serial.println("Card not found: " + uid);
  }
}

void processSerialCommand() {
  String command = Serial.readStringUntil('\n');
  command.trim();

  if (command.equalsIgnoreCase("reg")) {
    handleRegisterCommand();
  }
  else if (command.equalsIgnoreCase("list")) {
    handleListCommand();
  }
  else if (command.startsWith("del")) {
    String uid = command.substring(3);
    uid.trim();
    handleDeleteCommand(uid);
  }
  else {
    Serial.println("Unknown command");
    Serial.println("Available commands: reg, list, del <UID>");
    Serial.println("Note: Authentication is automatic when cards are detected");
  }
}

// =============================================================================
// 初始化
// =============================================================================
bool initializeFileSystem() {
  if (!SPIFFS.begin(true)) {
    Serial.println("SPIFFS Mount Failed");
    return false;
  }
  loadCards();
  return true;
}

bool initializeNFC() {
  Serial.println("=== PN532 初始化 ===");

  // 强制深度重置以清除任何错误状态
  Serial.println("1. 强制深度重置PN532...");
  performDeepPN532Reset();

  // 初始化PN532库
  Serial.println("2. 初始化PN532库...");
  nfc.begin();
  delay(200);

  // 尝试获取固件版本
  Serial.println("3. 获取固件版本...");
  uint32_t version = 0;
  for (int retry = 0; retry < 10; retry++) {
    Serial.print("   尝试 ");
    Serial.print(retry + 1);
    Serial.print("/10... ");

    version = nfc.getFirmwareVersion();
    if (version) {
      Serial.println("成功!");
      break;
    }
    Serial.println("失败");
    delay(300);
  }

  if (!version) {
    Serial.println("   ❌ 无法获取固件版本，但继续尝试");
    // 不返回false，继续尝试
  } else {
    Serial.print("   ✅ 固件版本: 0x");
    Serial.println(version, HEX);
  }

  // 配置SAM
  Serial.println("4. 配置SAM...");
  bool samSuccess = false;
  for (int retry = 0; retry < 5; retry++) {
    if (nfc.SAMConfig()) {
      Serial.println("   ✅ SAM配置成功");
      samSuccess = true;
      break;
    }
    Serial.print("   SAM配置失败，重试 ");
    Serial.println(retry + 1);
    delay(200);
  }

  if (!samSuccess) {
    Serial.println("   ❌ SAM配置失败，但继续运行");
  }

  Serial.println("=== PN532 初始化完成 ===");
  return true; // 总是返回true，让系统继续运行
}

void resetPN532State() {
  Serial.println("Performing PN532 soft reset...");

  // Just reconfigure SAM without hardware reset
  nfc.SAMConfig();
  delay(100);

  Serial.println("PN532 soft reset completed");
}

void performDeepPN532Reset() {
  Serial.println("Performing DEEP PN532 reset...");

  // ✅ 先重置I2C总线
  Wire.end();
  delay(200);

  // 多次硬件重置循环
  for (int cycle = 0; cycle < 3; cycle++) {
    Serial.print("Reset cycle ");
    Serial.println(cycle + 1);

    // 硬件重置
    digitalWrite(PN532_RESET, LOW);
    delay(300);
    digitalWrite(PN532_RESET, HIGH);
    delay(800);

    // 重新初始化I2C
    Wire.begin();
    delay(300);

    // 重新初始化PN532
    nfc.begin();
    delay(500);

    // 测试通信
    for (int test = 0; test < 5; test++) {
      uint32_t version = nfc.getFirmwareVersion();
      if (version) {
        Serial.print("Communication restored, firmware: 0x");
        Serial.println(version, HEX);

        if (nfc.SAMConfig()) {
          Serial.println("Deep reset successful");
          consecutiveErrors = 0;
          return;
        }
        break;
      }
      delay(300);
    }
  }

  Serial.println("Deep reset failed - system may need power cycle");
  consecutiveErrors = 0; // 重置计数器，避免无限重试
}


bool checkNFCHealth() {
  // 检查连续错误次数
  if (consecutiveErrors > 1) {
    Serial.print("NFC errors detected (");
    Serial.print(consecutiveErrors);
    Serial.println("), attempting recovery...");

    // ✅ 改进的恢复策略
    if (consecutiveErrors <= 2) {
      // 轻度恢复：I2C重置
      Serial.println("Attempting I2C recovery...");
      Wire.end();
      delay(100);
      Wire.begin();
      delay(200);

      nfc.begin();
      delay(300);

    } else if (consecutiveErrors <= 4) {
      // 中度恢复：硬件重置
      Serial.println("Attempting hardware reset...");
      digitalWrite(PN532_RESET, LOW);
      delay(100);
      digitalWrite(PN532_RESET, HIGH);
      delay(500);

      Wire.end();
      delay(100);
      Wire.begin();
      delay(200);

      nfc.begin();
      delay(300);

    } else {
      // 深度恢复
      Serial.println("Attempting deep recovery...");
      performDeepPN532Reset();
    }

    // 测试恢复效果
    uint32_t version = nfc.getFirmwareVersion();
    if (version && nfc.SAMConfig()) {
      Serial.println("Recovery successful");
      consecutiveErrors = 0;
      lastSuccessfulOperation = millis();
      return true;
    } else {
      Serial.println("Recovery failed");
      consecutiveErrors++;
      return false;
    }
  }
  return true;
}

void initializeLED() {
  pinMode(LED_PIN, OUTPUT);
  digitalWrite(LED_PIN, LOW);
}

void initializeGPIO() {
  // 初始化PN532相关引脚
  pinMode(PN532_IRQ, INPUT);
  pinMode(PN532_RESET, OUTPUT);

  // 确保RESET引脚初始状态为HIGH
  digitalWrite(PN532_RESET, HIGH);

  Serial.println("GPIO引脚初始化完成");
  Serial.print("IRQ引脚(");
  Serial.print(PN532_IRQ);
  Serial.print("): ");
  Serial.println(digitalRead(PN532_IRQ) ? "HIGH" : "LOW");
}

void printWelcomeMessage() {
  Serial.println("=================================");
  Serial.println("    Door Access System Ready    ");
  Serial.println("=================================");
  Serial.println("Commands:");
  Serial.println("  reg       - Register new card");
  Serial.println("  list      - List all cards");
  Serial.println("  del <UID> - Delete card");
  Serial.println("=================================");
  Serial.println("System will automatically detect");
  Serial.println("and authenticate cards.");
  Serial.println("=================================");
}

// =============================================================================
// 主函数
// =============================================================================
void setup() {
  Serial.begin(115200);
  delay(2000); // 等待串口稳定

  Serial.println("=================================");
  Serial.println("    系统启动中...    ");
  Serial.println("=================================");

  initializeLED();
  initializeGPIO();

  for (int i = 0; i < 3; i++) {
    blinkLED(1, 100);
    delay(100);
  }

  if (!initializeFileSystem()) {
    Serial.println("❌ 文件系统初始化失败");
    while (true) delay(1000);
  }
  Serial.println("✅ 文件系统初始化成功");

  if (!initializeNFC()) {
    Serial.println("❌ NFC读卡器初始化失败");
    Serial.println("系统将继续运行，但NFC功能不可用");
    Serial.println("请检查硬件连接后重启");
    while (true) {
      blinkLED(1, 1000); // 慢闪表示错误
      delay(2000);
    }
  }

  printWelcomeMessage();

  // Initialize state machine
  setState(WAITING_FOR_CARD);
  startNFCDetection();
}

void loop() {
  // Handle serial commands
  if (Serial.available()) {
    processSerialCommand();
  }

  // State machine processing
  switch (currentState) {
    case WAITING_FOR_CARD:
      handleWaitingForCard();
      break;

    case AUTHENTICATING:
      handleAuthenticating();
      break;

    case COOLDOWN:
      handleCooldown();
      break;

    case REGISTRATION_MODE:
      handleRegistrationMode();
      break;

    case REGISTERING:
      handleRegistering();
      break;
  }

  // Small delay to prevent excessive CPU usage
  delay(1);
}
